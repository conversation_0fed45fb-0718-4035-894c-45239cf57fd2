/**
 * 应用配置
 */

import { version } from '../package.json';

// 配置类型定义
interface AppConfig {
  title: string;
  icon: string;
  version: string;
  apiBaseUrl: string;
}

interface AgentConfig {
  strategy: '' | 'dify' | 'ragflow';
}

interface KnowledgeConfig {
  strategy: '' | 'ragflow';
  ragflow: {
    baseUrl: string;
    apiKey: string;
    defaultDatasetId: string;
  };
}

interface Config {
  app: AppConfig;
  agent: AgentConfig;
  knowledge: KnowledgeConfig;
}

// 默认配置
const defaultConfig: Config = {
  app: {
    title: '碧慧知识库系统',
    icon: '/favicon.ico',
    version: version,
    apiBaseUrl: '/api'
  },
  agent: {
    strategy: '',
  },
  knowledge: {
    strategy: 'ragflow',
    ragflow: {
      baseUrl: '',
      apiKey: '',
      defaultDatasetId: ''
    }
  }
};

// 运行时配置
let runtimeConfig: Config = defaultConfig;

// 配置加载函数
export function loadConfig(config: Partial<Config>) {
  // 合并应用配置
  if (config.app) {
    runtimeConfig.app = {
      title: config.app.title ?? defaultConfig.app.title,
      icon: config.app.icon ?? defaultConfig.app.icon,
      version: config.app.version ?? defaultConfig.app.version,
      apiBaseUrl: config.app.apiBaseUrl ?? defaultConfig.app.apiBaseUrl
    };
  }

  // 合并Agent配置
  if (config.agent) {
    runtimeConfig.agent = {
      strategy: config.agent.strategy ?? defaultConfig.agent.strategy
    };
  }

  // 合并知识库配置
  if (config.knowledge) {
    runtimeConfig.knowledge = {
      strategy: config.knowledge.strategy ?? defaultConfig.knowledge.strategy,
      ragflow: {
        baseUrl: config.knowledge.ragflow?.baseUrl ?? defaultConfig.knowledge.ragflow.baseUrl,
        apiKey: config.knowledge.ragflow?.apiKey ?? defaultConfig.knowledge.ragflow.apiKey,
        defaultDatasetId: config.knowledge.ragflow?.defaultDatasetId ?? defaultConfig.knowledge.ragflow.defaultDatasetId
      }
    };
  }
}

// 获取当前配置
export function getConfig(): Config {
  return runtimeConfig;
}

// 导出配置
export const { app, agent, knowledge } = getConfig();

// 导出默认配置
export default getConfig();
