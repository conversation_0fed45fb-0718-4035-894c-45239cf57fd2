export interface Document {
  id: string;
  name?: string;
  content?: string;
  type?: string;
  size?: number;
  created_at?: string;
  updated_at?: string;
  metadata?: Record<string, any>;
  run?: string;
  status?: string;
  // 新增预览相关属性
  blob?: Blob;
  blobUrl?: string;
  previewUrl?: string;
  downloadUrl?: string;
}

export interface DocumentChunk {
  id: string;
  document_id: string;
  content: string;
  embedding?: number[];
  metadata?: Record<string, any>;
  created_at: string;
}
