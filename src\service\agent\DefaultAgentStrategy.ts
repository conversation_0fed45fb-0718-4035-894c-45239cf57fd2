import { AgentStrategy, AgentConfig, AgentResponse } from './AgentStrategy';

export class DefaultAgentStrategy implements AgentStrategy {
  private config: AgentConfig = {
    name: 'default-agent',
    description: '默认代理',
    model: 'gpt-3.5-turbo',
    temperature: 0.7,
    maxTokens: 2000,
    systemPrompt: '你是一个智能助手，可以帮助用户回答问题。',
  };

  async initialize(config: AgentConfig): Promise<void> {
    this.config = { ...this.config, ...config };
  }

  async sendMessage(message: string, context?: Record<string, any>): Promise<AgentResponse> {
    // TODO: 实现默认的消息处理逻辑
    return {
      content: `这是默认代理的回复: ${message}`,
      metadata: {
        model: this.config.model,
        timestamp: new Date().toISOString(),
      },
    };
  }

  getConfig(): AgentConfig {
    return { ...this.config };
  }

  async updateConfig(config: Partial<AgentConfig>): Promise<void> {
    this.config = { ...this.config, ...config };
  }

  async reset(): Promise<void> {
    // TODO: 实现重置逻辑
  }
}
