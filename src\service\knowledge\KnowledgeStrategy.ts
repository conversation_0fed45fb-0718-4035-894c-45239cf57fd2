import { Document, DocumentChunk } from '@/types/Document';

export interface DocumentMetadata {
  location?: string;
  chunk_count?: number;
  run?: string;
  [key: string]: any;
}

export interface SearchResult {
  documents: Document[];
  total: number;
  page: number;
  pageSize: number;
}

export interface KnowledgeStrategy {
  // 设置数据集ID
  setDatasetId(datasetId: string): void;

  // 创建数据集
  createDataset(name: string, description?: string): Promise<string>;

  // 获取数据集列表
  listDatasets(): Promise<any[]>;

  // 上传文档
  uploadDocument(file: File, metadata?: DocumentMetadata): Promise<Document>;

  // 搜索文档
  searchDocuments(query: string, page?: number, pageSize?: number): Promise<SearchResult>;

  // 获取文档
  getDocument(id: string): Promise<Document>;

  // 更新文档
  updateDocument(id: string, data: Partial<Document>): Promise<Document>;

  // 删除文档
  deleteDocument(id: string): Promise<void>;

  // 获取文档列表
  listDocuments(page?: number, pageSize?: number, filters?: { 
    keywords?: string;
    orderby?: string;
    desc?: boolean;
  }): Promise<SearchResult>;

  // 解析文档
  parseDocument(id: string): Promise<void>;

  // 获取文档块
  getDocumentChunks(id: string, page?: number, pageSize?: number): Promise<{ chunks: DocumentChunk[] }>;
}
