import { createApp } from 'vue';
import { createPinia } from 'pinia';
import PrimeVue from 'primevue/config';
import ToastService from 'primevue/toastservice';
import ConfirmationService from 'primevue/confirmationservice';
import App from './App.vue';
import router from './router';
import './utils/axios';
import { loadConfig } from './config';
import { useAppStore } from './stores/app';

import { definePreset } from '@primeuix/themes';
import Material from '@primeuix/themes/material';

// Styles
import 'primeicons/primeicons.css';
import './styles/main.css';

// 加载外部配置
async function loadExternalConfig() {
  try {
    const response = await fetch('/config.json');
    if (response.ok) {
      const config = await response.json();
      loadConfig(config);
      return true;
    }
  } catch (error) {
    console.warn('Failed to load external config:', error);
  }
  return false;
}

// 初始化应用
async function initApp() {
  const app = createApp(App);
  const pinia = createPinia();
  app.use(pinia);

  // 加载外部配置
  const configLoaded = await loadExternalConfig();
  
  // 初始化 store
  const appStore = useAppStore();
  if (configLoaded) {
    appStore.updateConfig();
  }

  const MyPreset = definePreset(Material, {
    semantic: {
      primary: {
        50: '{emerald.50}',
        100: '{emerald.100}',
        200: '{emerald.200}',
        300: '{emerald.300}',
        400: '{emerald.400}',
        500: '{emerald.500}',
        600: '{emerald.600}',
        700: '{emerald.700}',
        800: '{emerald.800}',
        900: '{emerald.900}',
        950: '{emerald.950}'
      }
    }
  });

  // Use plugins
  app.use(router);
  app.use(PrimeVue, {
    theme: {
      preset: MyPreset
    },
    ripple: true,
    inputStyle: 'filled',
    zIndex: {
      modal: 1100,
      overlay: 1000,
      menu: 1000,
      tooltip: 1100,
    },
  });
  app.use(ToastService);
  app.use(ConfirmationService);

  app.mount('#app');
}

// 启动应用
initApp();
