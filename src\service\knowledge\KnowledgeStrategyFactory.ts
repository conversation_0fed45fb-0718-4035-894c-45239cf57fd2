import { KnowledgeStrategy } from './KnowledgeStrategy';
import { RAGFlowKnowledgeStrategy } from './RAGFlowKnowledgeStrategy';
import config from '@/config';

export class KnowledgeStrategyFactory {
  static createStrategy(): KnowledgeStrategy {
    switch (config.knowledge.strategy) {
      case 'ragflow':
        const ragflowConfig = config.knowledge.ragflow;
        return new RAGFlowKnowledgeStrategy(ragflowConfig.baseUrl, ragflowConfig.apiKey, ragflowConfig.defaultDatasetId);
      case 'dify':
        // TODO: 实现 Dify 知识库策略
        throw new Error('Dify 知识库策略尚未实现');
      case 'default':
      default:
        // TODO: 实现默认知识库策略
        throw new Error('默认知识库策略尚未实现');
    }
  }
}
