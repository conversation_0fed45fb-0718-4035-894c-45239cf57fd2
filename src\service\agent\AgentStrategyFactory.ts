import { AgentStrategy } from './AgentStrategy';
import { DefaultAgentStrategy } from './DefaultAgentStrategy';
import { DifyAgentStrategy } from './DifyAgentStrategy';
import { RAGFlowAgentStrategy } from './RAGFlowAgentStrategy';
import config from '../../config';

export class AgentStrategyFactory {
  static createStrategy(): AgentStrategy {
    switch (config.api.agent?.strategy) {
      case 'dify':
        return new DifyAgentStrategy();
      case 'ragflow':
        return new RAGFlowAgentStrategy();
      case 'default':
      default:
        return new DefaultAgentStrategy();
    }
  }
}
