import axios, { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse, AxiosStatic } from 'axios';
import { useToast } from 'primevue/usetoast';

// 创建axios实例
const http: AxiosInstance & { create: AxiosStatic['create'] } = Object.assign(
  axios.create({
    baseURL: '/api',
    timeout: 30000, // 30秒超时
    headers: {
      'Content-Type': 'application/json',
    },
  }),
  { create: axios.create }
);

// 请求拦截器
http.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 在这里可以添加token等认证信息
    // const token = localStorage.getItem('token')
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`
    // }
    return config;
  },
  (error: any) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
http.interceptors.response.use(
  (response: AxiosResponse) => {
    return response.data;
  },
  (error: any) => {
    const toast = useToast();

    // 处理错误响应
    let message = '请求失败';
    if (error.response) {
      // 服务器返回错误状态码
      switch (error.response.status) {
        case 400:
          message = error.response.data.message || '请求参数错误';
          break;
        case 401:
          message = '未授权，请重新登录';
          // 可以在这里处理登录过期的逻辑
          break;
        case 403:
          message = '拒绝访问';
          break;
        case 404:
          message = '请求的资源不存在';
          break;
        case 500:
          message = '服务器错误';
          break;
        default:
          message = error.response.data.message || `请求失败(${error.response.status})`;
      }
    } else if (error.request) {
      // 请求发出但没有收到响应
      message = '无法连接到服务器';
    } else {
      // 请求配置出错
      message = error.message;
    }

    // 显示错误提示
    toast.add({
      severity: 'error',
      summary: '错误',
      detail: message,
      life: 3000,
    });

    return Promise.reject(error);
  }
);

export default http;
