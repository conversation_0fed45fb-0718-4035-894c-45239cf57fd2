import { KnowledgeStrategy, SearchResult, DocumentMetadata } from './KnowledgeStrategy';
import { Document, DocumentChunk } from '@/types/Document';
import config from '@/config';
import axios, { AxiosInstance } from 'axios';

// 定义RAGFlow API返回的数据类型
interface RAGFlowResponse<T = any> {
  data: T;
  message?: string;
  code?: number;
}

export class RAGFlowKnowledgeStrategy implements KnowledgeStrategy {
  private baseUrl: string;
  private apiKey: string;
  private datasetId: string;
  private axiosInstance: AxiosInstance;

  constructor(baseUrl: string = config.knowledge.ragflow.baseUrl, apiKey: string = config.knowledge.ragflow.apiKey, datasetId?: string) {
    this.baseUrl = baseUrl;
    this.apiKey = apiKey;
    this.datasetId = datasetId || config.knowledge.ragflow.defaultDatasetId || '';

    // 创建专用的 axios 实例
    this.axiosInstance = axios.create({
      baseURL: this.baseUrl,
      headers: {
        Authorization: `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      timeout: 30000,
    });

    // 添加响应拦截器
    this.axiosInstance.interceptors.response.use(
      (response) => {
        return response.data;
      },
      (error) => {
        console.error('RAGFlow API请求失败:', error);
        return Promise.reject(error);
      }
    );
  }

  setDatasetId(datasetId: string): void {
    this.datasetId = datasetId;
  }

  async createDataset(name: string, description?: string): Promise<string> {
    try {
      const response = await this.axiosInstance.post('/api/v1/datasets', {
        name,
        description,
        parser_config: {
          chunk_size: 500,
          chunk_overlap: 50,
          chunk_by: 'paragraph',
        },
      });

      return response.data.id;
    } catch (error) {
      console.error('创建数据集失败:', error);
      throw error;
    }
  }

  async listDatasets(): Promise<any[]> {
    try {
      const response = await this.axiosInstance.get('/api/v1/datasets');
      return response.data || [];
    } catch (error) {
      console.error('获取数据集列表失败:', error);
      throw error;
    }
  }

  async uploadDocument(file: File, metadata?: DocumentMetadata): Promise<Document> {
    if (!this.datasetId) {
      throw new Error('请先设置数据集ID');
    }

    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await this.axiosInstance.post(`/api/v1/datasets/${this.datasetId}/documents`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      const doc = response.data[0];
      return {
        id: doc.id,
        name: doc.name,
        content: '',
        type: doc.type,
        size: doc.size,
        created_at: doc.create_time,
        updated_at: doc.update_time,
        run: doc.run || 'UNSTART',
        status: doc.status || '0',
        metadata: {
          location: doc.location,
          ...metadata,
        },
      };
    } catch (error) {
      console.error('上传文档失败:', error);
      throw error;
    }
  }

  async searchDocuments(query: string, page: number = 1, pageSize: number = 10): Promise<SearchResult> {
    if (!this.datasetId) {
      throw new Error('请先设置数据集ID');
    }

    try {
      const response = await this.axiosInstance.post('/api/v1/retrieval', {
        question: query,
        dataset_ids: [this.datasetId],
        page,
        page_size: pageSize,
        similarity_threshold: 0.2,
        vector_similarity_weight: 0.3,
        top_k: 1024,
        keyword: false,
        highlight: true,
      });

      const chunks = response.data.chunks || [];
      const documents = chunks.map((chunk: any) => ({
        id: chunk.id,
        name: chunk.document_keyword || '未知文档',
        content: chunk.content || '',
        type: 'text',
        size: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        run: 'DONE',
        status: '1',
        metadata: {
          document_id: chunk.document_id,
          similarity: chunk.similarity,
          vector_similarity: chunk.vector_similarity,
          term_similarity: chunk.term_similarity,
        },
      }));

      return {
        documents,
        total: response.data.total || 0,
        page,
        pageSize,
      };
    } catch (error) {
      console.error('搜索文档失败:', error);
      throw error;
    }
  }

  async getDocument(id: string): Promise<Document> {
    if (!this.datasetId) {
      throw new Error('请先设置数据集ID');
    }

    try {
      // 创建一个新的axios实例，不使用拦截器，专门用于获取blob数据
      const blobAxios = axios.create({
        baseURL: this.baseUrl,
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
        },
        timeout: 30000,
      });

      // 获取文档的blob数据用于预览
      const response = await blobAxios.get(`/api/v1/datasets/${this.datasetId}/documents/${id}`, {
        responseType: 'blob',
      });

      const blob = response.data as Blob;
      const blobUrl = URL.createObjectURL(blob);

      // 构建下载URL
      const downloadUrl = `${this.baseUrl}/api/v1/datasets/${this.datasetId}/documents/${id}`;

      return {
        id,
        blob,
        blobUrl,
        previewUrl: blobUrl, // 使用blob URL进行预览
        downloadUrl,
        type: blob.type,
        size: blob.size
      };
    } catch (error) {
      console.error('获取文档失败:', error);
      throw error;
    }
  }

  async updateDocument(id: string, data: Partial<Document>): Promise<Document> {
    if (!this.datasetId) {
      throw new Error('请先设置数据集ID');
    }

    try {
      await this.axiosInstance.put(`/api/v1/datasets/${this.datasetId}/documents/${id}`, {
        name: data.name,
        meta_fields: data.metadata,
      });

      return this.getDocument(id);
    } catch (error) {
      console.error('更新文档失败:', error);
      throw error;
    }
  }

  async deleteDocument(id: string): Promise<void> {
    if (!this.datasetId) {
      throw new Error('请先设置数据集ID');
    }

    try {
      await this.axiosInstance.delete(`/api/v1/datasets/${this.datasetId}/documents`, {
        data: {
          ids: [id],
        },
      });
    } catch (error) {
      console.error('删除文档失败:', error);
      throw error;
    }
  }

  async listDocuments(
    page: number = 1,
    pageSize: number = 10,
    { keywords = '', orderby = 'create_time', desc = true }: { keywords?: string; orderby?: string; desc?: boolean } = {}
  ): Promise<SearchResult> {
    if (!this.datasetId) {
      throw new Error('请先设置数据集ID');
    }

    try {
      const response = await this.axiosInstance.get(`/api/v1/datasets/${this.datasetId}/documents`, {
        params: {
          page,
          page_size: pageSize,
          // orderby, // 排序字段，默认为创建时间
          // desc, // 是否降序排列，默认为true
          keywords, // 用于匹配文档标题的关键词
          dataset_id: this.datasetId, // 关联的数据集ID
        },
      });

      const docs = response.data.docs;
      const documents = docs.map((doc: any) => ({
        id: doc.id,
        name: doc.name,
        content: '',
        type: doc.type,
        size: doc.size,
        created_at: doc.create_time,
        updated_at: doc.update_time,
        run: doc.run || 'UNSTART',
        status: doc.status || '0',
        metadata: {
          location: doc.location,
          chunk_count: doc.chunk_count,
          run: doc.run,
          status: doc.status,
        },
      }));

      return {
        documents,
        total: response.data.total,
        page,
        pageSize,
      };
    } catch (error) {
      console.error('获取文档列表失败:', error);
      throw error;
    }
  }

  async parseDocument(id: string): Promise<void> {
    if (!this.datasetId) {
      throw new Error('请先设置数据集ID');
    }

    try {
      await this.axiosInstance.post(`/api/v1/datasets/${this.datasetId}/chunks`, {
        document_ids: [id],
      });
    } catch (error) {
      console.error('解析文档失败:', error);
      throw error;
    }
  }

  async getDocumentChunks(id: string, page: number = 1, pageSize: number = 10): Promise<{ chunks: DocumentChunk[] }> {
    if (!this.datasetId) {
      throw new Error('请先设置数据集ID');
    }

    try {
      const response = await this.axiosInstance.get(`/api/v1/datasets/${this.datasetId}/documents/${id}/chunks`, {
        params: {
          page,
          page_size: pageSize,
        },
      });

      return {
        chunks: response.data.chunks.map((chunk: any) => ({
          id: chunk.id,
          document_id: id,
          content: chunk.content,
          embedding: chunk.embedding,
          metadata: chunk.metadata,
          created_at: chunk.create_time,
        })),
      };
    } catch (error) {
      console.error('获取文档chunks失败:', error);
      throw error;
    }
  }
}
