interface UploadConfig {
  acceptedFileTypes: string[];
  maxFileSize: number;
  fileTypeDescriptions: Record<string, string>;
}

interface RAGFlowConfig {
  baseUrl: string;
  apiKey: string;
  defaultDatasetId?: string;
}

interface DifyConfig {
  baseUrl: string;
  apiKey: string;
  appId?: string;
}

interface DefaultAPIConfig {
  baseURL: string;
  timeout: number;
  endpoints: {
    upload: string;
    download: string;
    list: string;
    delete: string;
  };
}

interface APIConfig {
  // API策略: 'default' | 'dify' | 'ragflow'
  strategy: 'default' | 'dify' | 'ragflow';
  ragflow: RAGFlowConfig;
  dify: DifyConfig;
  default: DefaultAPIConfig;
}

interface UIConfig {
  rowsPerPageOptions: number[];
  defaultRowsPerPage: number;
  toastDuration: number;
  fileSizeUnits: string[];
}

interface PreviewConfig {
  supportedTypes: string[];
  defaultWidth: string;
  defaultHeight: string;
}

interface KnowledgeConfig {
  strategy: 'ragflow' | 'default' | '';
  ragflow?: {
    baseUrl: string;
    apiKey: string;
    defaultDatasetId?: string;
  };
}

interface AgentConfig {
  strategy: 'dify' | 'ragflow' | 'default' | '';
  ragflow?: {
    baseUrl: string;
    apiKey: string;
  };
  dify?: {
    baseUrl: string;
    apiKey: string;
  };
  openai?: {
    apiKey: string;
    model: string;
    temperature?: number;
    maxTokens?: number;
  };
  anthropic?: {
    apiKey: string;
    model: string;
    temperature?: number;
    maxTokens?: number;
  };
}

interface AppConfig {
  title: string;
  description: string;
  version: string;
  apiBaseUrl: string;
}

interface Config {
  app: AppConfig;
  upload: UploadConfig;
  api: APIConfig;
  ui: UIConfig;
  preview: PreviewConfig;
  agent: AgentConfig;
  knowledge: KnowledgeConfig;
}

export const config: Config = {
  // 文件上传配置
  upload: {
    // 支持的文件类型
    acceptedFileTypes: [
      'application/pdf', // PDF
      'application/msword', // DOC
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // DOCX
      'application/vnd.ms-excel', // XLS
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // XLSX
      'application/vnd.ms-powerpoint', // PPT
      'application/vnd.openxmlformats-officedocument.presentationml.presentation', // PPTX
      'text/plain', // TXT
    ],
    // 文件大小限制（50MB）
    maxFileSize: 50 * 1024 * 1024,
    // 文件类型说明
    fileTypeDescriptions: {
      'application/pdf': 'PDF文档',
      'application/msword': 'Word文档',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word文档',
      'application/vnd.ms-excel': 'Excel表格',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel表格',
      'application/vnd.ms-powerpoint': 'PowerPoint演示文稿',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'PowerPoint演示文稿',
      'text/plain': '文本文件',
    },
  },

  // API配置
  api: {
    // API策略: 'default' | 'dify' | 'ragflow'
    strategy: 'ragflow',
    // RAGFlow配置
    ragflow: {
      // 服务器地址
      baseUrl: 'http://*************:680',
      // API密钥
      apiKey: 'ragflow-Y2N2FjODIwMDk0YTExZjA5ZDdmMDI0Mm',
      // 默认数据集ID（可选）
      defaultDatasetId: undefined,
    },
    // Dify配置
    dify: {
      // 服务器地址
      baseUrl: 'https://api.dify.ai/v1',
      // API密钥
      apiKey: 'your-dify-api-key',
      // 应用ID（可选）
      appId: undefined,
    },
    // 默认API配置
    default: {
      // 基础URL
      baseURL: '/api',
      // 超时时间（毫秒）
      timeout: 30000,
      // 上传相关接口
      endpoints: {
        upload: '/documents/upload',
        download: '/documents/download',
        list: '/documents',
        delete: '/documents',
      },
    },
  },

  // UI配置
  ui: {
    // 每页显示的文档数量选项
    rowsPerPageOptions: [10, 20, 50],
    // 默认每页显示数量
    defaultRowsPerPage: 10,
    // Toast消息的默认显示时间（毫秒）
    toastDuration: 3000,
    // 文件大小格式化单位
    fileSizeUnits: ['B', 'KB', 'MB', 'GB', 'TB'],
  },

  // 文档预览配置
  preview: {
    // 支持预览的文件类型
    supportedTypes: ['application/pdf', 'text/plain'],
    // 预览窗口默认宽度
    defaultWidth: '80vw',
    // 预览窗口默认高度
    defaultHeight: '80vh',
  },
};

export default config;
