<template>
  <div class="flex min-h-screen flex-col">
    <header class="shadow-sm">
      <Menubar :model="menuItems">
        <template #start>
          <img src="/logo.png" alt="Logo" width="40" height="40" class="mr-2" />
          <span class="text-xl font-bold">{{ appStore.appTitle }}</span>
        </template>

        <template #item="{ item, props }">
          <router-link
            v-if="item.route"
            v-slot="{ href, navigate }"
            :to="item.route"
            custom
          >
            <a v-ripple :href="href" v-bind="props.action" @click="navigate">
              <span class="p-menubar-item-icon" :class="item.icon" />
              <span class="p-menubar-item-label">{{ item.label }}</span>
            </a>
          </router-link>
          <a v-else v-ripple :href="item.url" :target="item.target" v-bind="props.action">
            <span class="p-menubar-item-icon" :class="item.icon" />
            <span class="p-menubar-item-label">{{ item.label }}</span>
          </a>
        </template>
      </Menubar>
    </header>

    <main class="flex-1 bg-gray-50 p-8">
      <router-view></router-view>
    </main>
  </div>
</template>

<script setup>
import { ref } from "vue";
import Menubar from "primevue/menubar";
import { useAppStore } from "@/stores/app";

const appStore = useAppStore();
const menuItems = ref([
  {
    label: "文档管理",
    icon: "pi pi-fw pi-file",
    route: "/",
  },
  {
    label: "知识检索",
    icon: "pi pi-fw pi-search",
    route: "/search",
  },
]);
</script>

<style>
/* 全局样式 */
:root {
  --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue",
    Arial, sans-serif;
  --text-color: #2c3e50;
  --surface-ground: #f8f9fa;
}
</style>
