<template>
  <Dialog
    :visible="visible"
    :header="document?.title || '文档预览'"
    :style="{ width: '90vw', height: '90vh' }"
    :modal="true"
    :closable="true"
    :draggable="false"
    :resizable="true"
    class="document-preview-dialog"
    @update:visible="handleVisibleChange"
    @hide="handleClose"
  >
    <!-- 文档预览标题 -->
    <template #header>
      <!-- 文档信息头部 -->
      <div v-if="document" class="document-info">
        <div class="info-item">
          <span class="label">文件名：</span>
          <span class="value">{{ document.title }}</span>
        </div>
        <div class="info-item">
          <span class="label">类型：</span>
          <Tag :value="document.type" />
        </div>
        <div class="info-item">
          <span class="label">大小：</span>
          <span class="value">{{ formatFileSize(document.size) }}</span>
        </div>
        <div class="info-item">
          <span class="label">上传时间：</span>
          <span class="value">{{ formatDateLocale(document.uploadTime) }}</span>
        </div>
      </div>
      <div v-else class="align-items-center flex gap-2">
        <span>{{ document?.title || '文档预览' }}</span>
        <Tag severity="info" value="文档预览" class="text-sm" />
      </div>
    </template>
    <div class="preview-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <ProgressSpinner style="width: 50px; height: 50px" strokeWidth="4" />
        <p class="mt-3">正在加载文档...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-container">
        <i class="pi pi-exclamation-triangle" style="font-size: 3rem; color: #e74c3c"></i>
        <p class="mt-3">{{ error }}</p>
        <Button label="重试" icon="pi pi-refresh" @click="() => {}" class="mt-3" />
      </div>

      <!-- 文档预览内容 -->
      <div v-else-if="document" class="preview-content">
        <!-- 预览内容区域 -->
        <div class="preview-area">
          <!-- 根据文件类型选择预览方式 -->
          <div class="document-preview">
            <!-- PDF 文件预览 -->
            <object
              v-if="documentUrl && isPdfFile"
              :data="documentUrl"
              type="application/pdf"
              width="100%"
              height="600px"
              style="border: none"
            >
              <div class="pdf-fallback">
                <p>您的浏览器不支持 PDF 预览</p>
                <p class="text-sm text-gray-500">请使用下方的下载按钮获取文件</p>
              </div>
            </object>

            <!-- 图片文件预览 -->
            <div v-else-if="documentUrl && isImageFile" class="image-preview">
              <img :src="documentUrl" alt="图片预览" style="max-width: 100%; max-height: 600px; object-fit: contain;" />
            </div>

            <!-- 文本文件预览 -->
            <div v-else-if="documentUrl && isTextFile" class="text-content">
              <pre>{{ textContent }}</pre>
            </div>

            <!-- 其他文件类型或无法预览 -->
            <div v-else-if="documentUrl" class="unsupported-preview">
              <i class="pi pi-file" style="font-size: 3rem; color: #6c757d"></i>
              <p>此文件类型暂不支持在线预览</p>
              <p class="text-sm text-gray-500">文件类型: {{ document?.type || 'unknown' }}</p>
              <Button label="下载查看" icon="pi pi-download" @click="downloadDocument" class="mt-3" />
            </div>

            <!-- 加载中或无文档 -->
            <div v-else class="no-preview">
              <i class="pi pi-file" style="font-size: 3rem; color: #6c757d"></i>
              <p>文档预览</p>
              <p class="text-sm text-gray-500">正在加载文档内容...</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <Button v-if="documentUrl" label="在新窗口中打开" icon="pi pi-external-link" @click="openInNewWindow" class="p-button-outlined mr-2" />
        <Button v-if="documentUrl" label="下载" icon="pi pi-download" @click="downloadDocument" class="p-button-outlined mr-2" />
        <Button label="关闭" icon="pi pi-times" @click="handleClose" />
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useToast } from 'primevue/usetoast';
import Dialog from 'primevue/dialog';
import Button from 'primevue/button';
import ProgressSpinner from 'primevue/progressspinner';
import Tag from 'primevue/tag';
import { formatFileSize, formatDateLocale } from '@/utils/helpers';
import { KnowledgeStrategyFactory } from '@/service/knowledge/KnowledgeStrategyFactory';

// 响应式数据
const visible = ref(false);
const loading = ref(false);
const error = ref('');
const document = ref<any>(null);
const documentUrl = ref('');
const textContent = ref('');

// Toast
const toast = useToast();

// 知识库服务
const knowledgeService = KnowledgeStrategyFactory.createStrategy();

// 文件类型常量
const IMAGE_EXTENSIONS = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
const TEXT_EXTENSIONS = ['txt', 'md', 'json', 'xml', 'csv', 'log', 'js', 'ts', 'css', 'html', 'htm'];

// 文件类型检测函数
function getFileExtension(fileName: string): string {
  return fileName.toLowerCase().split('.').pop() || '';
}



// 计算属性 - 根据文件名后缀判断文件类型
const isPdfFile = computed(() => {
  const fileName = document.value?.title || document.value?.name || '';
  return getFileExtension(fileName) === 'pdf';
});

const isImageFile = computed(() => {
  const fileName = document.value?.title || document.value?.name || '';
  const ext = getFileExtension(fileName);
  return IMAGE_EXTENSIONS.includes(ext);
});

const isTextFile = computed(() => {
  const fileName = document.value?.title || document.value?.name || '';
  const ext = getFileExtension(fileName);
  return TEXT_EXTENSIONS.includes(ext);
});

// 显示文档预览
async function show(doc: any) {
  if (!doc) {
    error.value = '文档数据不可用';
    return;
  }

  // 显示对话框
  visible.value = true;
  loading.value = true;
  error.value = '';

  try {
    // 设置基本文档信息
    document.value = {
      ...doc,
      metadata: doc.metadata || {}
    };

    // 如果文档有datasetId，设置到知识库服务
    if (doc.datasetId) {
      knowledgeService.setDatasetId(doc.datasetId);
    }

    // 使用knowledgeService获取文档的实际内容
    const documentData = await knowledgeService.getDocument(doc.id);

    // 更新文档数据，包含从API获取的blob和blobUrl
    document.value = {
      ...document.value,
      // type: documentData.type,
      size: documentData.size,
      metadata: {
        ...document.value.metadata,
        blobUrl: documentData.blobUrl,
        originalData: documentData.blob,
        contentType: documentData.type || 'application/octet-stream'
      }
    };

    // 如果是文本文件，读取内容
    const fileName = doc.title || doc.name || '';
    const ext = getFileExtension(fileName);
    if (documentData.blob && TEXT_EXTENSIONS.includes(ext)) {
      try {
        const text = await documentData.blob.text();
        textContent.value = text;
      } catch (textError) {
        console.warn('读取文本内容失败:', textError);
      }
    }

    // 生成文档URL
    generateDocumentUrl();

  } catch (err) {
    console.error('获取文档内容失败:', err);
    error.value = err instanceof Error ? err.message : '获取文档内容失败';

    toast.add({
      severity: 'error',
      summary: '预览失败',
      detail: error.value,
      life: 3000
    });
  } finally {
    loading.value = false;
  }
}

// 隐藏文档预览
function hide() {
  visible.value = false;
  cleanupBlobUrl();
  document.value = null;
  documentUrl.value = '';
  textContent.value = '';
  error.value = '';
}

// 生成文档URL
function generateDocumentUrl() {
  if (!document.value) return;

  // 如果文档元数据中有blobUrl，使用它（从API获取的数据）
  if (document.value.metadata?.blobUrl) {
    documentUrl.value = document.value.metadata.blobUrl;
    return;
  }

  // 对于有内容的文档，不需要URL
  if (document.value.content) {
    return;
  }
}

// 下载文档
function downloadDocument() {
  if (!document.value) {
    toast.add({
      severity: 'warn',
      summary: '无法下载',
      detail: '文档数据不可用',
      life: 3000,
    });
    return;
  }

  // 优先使用原始数据进行下载
  if (document.value.metadata?.originalData) {
    const blob = new Blob([document.value.metadata.originalData], {
      type: document.value.metadata.contentType || 'application/octet-stream',
    });
    const url = URL.createObjectURL(blob);

    const link = window.document.createElement('a');
    link.href = url;
    link.download = document.value.title || 'document';
    window.document.body.appendChild(link);
    link.click();
    window.document.body.removeChild(link);

    // 清理URL
    URL.revokeObjectURL(url);
  } else if (documentUrl.value) {
    // 备用方案：使用URL下载
    const link = window.document.createElement('a');
    link.href = documentUrl.value;
    link.download = document.value.title || 'document';
    link.target = '_blank';
    window.document.body.appendChild(link);
    link.click();
    window.document.body.removeChild(link);
  } else {
    toast.add({
      severity: 'warn',
      summary: '无法下载',
      detail: '文档链接不可用',
      life: 3000,
    });
  }
}

// 在新窗口中打开文档
function openInNewWindow() {
  if (!documentUrl.value) {
    toast.add({
      severity: 'warn',
      summary: '无法打开',
      detail: '文档链接不可用',
      life: 3000,
    });
    return;
  }

  window.open(documentUrl.value, '_blank');
}

// 处理visible变化
function handleVisibleChange(newVisible: boolean) {
  visible.value = newVisible;
}

// 清理Blob URL
function cleanupBlobUrl() {
  if (document.value?.metadata?.blobUrl) {
    URL.revokeObjectURL(document.value.metadata.blobUrl);
  }
}

// 关闭对话框
function handleClose() {
  hide();
}

// 暴露给父组件的方法
defineExpose({
  show,
  hide,
});
</script>

<style scoped>
.document-preview-dialog {
  --dialog-border-radius: 12px;
}

.preview-container {
  height: calc(90vh - 120px);
  display: flex;
  flex-direction: column;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
}

.preview-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.document-info {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.info-item .label {
  font-weight: 600;
  color: #6c757d;
}

.info-item .value {
  color: #495057;
}

.preview-area {
  flex: 1;
  overflow: auto;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  background: #fff;
}

.document-preview {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.document-preview iframe {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.text-content {
  width: 100%;
  height: 100%;
  padding: 1rem;
  overflow: auto;
}

.text-content pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

.no-preview,
.unsupported-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #6c757d;
  padding: 2rem;
}

.no-preview p,
.unsupported-preview p {
  margin: 0.5rem 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.image-preview img {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.pdf-preview {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.pdf-preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  border-radius: 8px 8px 0 0;
}

.pdf-preview-header span {
  font-weight: 600;
  color: #495057;
}

.pdf-actions {
  display: flex;
  gap: 0.5rem;
}

.pdf-preview object {
  flex: 1;
  border-radius: 0 0 8px 8px;
}

.pdf-fallback {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  color: #6c757d;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .document-info {
    flex-direction: column;
    gap: 0.5rem;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>
