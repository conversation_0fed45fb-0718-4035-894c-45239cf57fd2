import { defineStore } from 'pinia';
import { ref } from 'vue';
import config from '@/config';

export const useAppStore = defineStore('app', () => {
  // 应用信息
  const appTitle = ref('');
  const appVersion = ref('');
  const appIcon = ref('');
  const apiBaseUrl = ref('');
  
  // 配置状态
  const agentStrategy = ref('');
  const knowledgeStrategy = ref('');
  const knowledgeConfig = ref({
    baseUrl: '',
    apiKey: '',
    defaultDatasetId: ''
  });

  // 更新配置
  function updateConfig() {
    appTitle.value = config.app.title;
    appVersion.value = config.app.version;
    appIcon.value = config.app.icon;
    apiBaseUrl.value = config.app.apiBaseUrl;
    
    agentStrategy.value = config.agent.strategy;
    knowledgeStrategy.value = config.knowledge.strategy;
    knowledgeConfig.value = config.knowledge.ragflow;
  }

  // 方法
  function setAgentStrategy(strategy: typeof config.agent.strategy) {
    agentStrategy.value = strategy;
  }
  
  function setKnowledgeStrategy(strategy: typeof config.knowledge.strategy) {
    knowledgeStrategy.value = strategy;
  }
  
  return {
    appTitle,
    appVersion,
    appIcon,
    apiBaseUrl,
    agentStrategy,
    knowledgeStrategy,
    knowledgeConfig,
    updateConfig,
    setAgentStrategy,
    setKnowledgeStrategy,
  };
});
