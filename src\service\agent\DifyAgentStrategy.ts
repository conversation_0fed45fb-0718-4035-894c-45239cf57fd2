import { AgentStrategy, AgentConfig, AgentResponse } from './AgentStrategy';
import config from '../../config';
import http from '../../utils/axios';

export class DifyAgentStrategy implements AgentStrategy {
  private config: AgentConfig;
  private baseUrl: string;
  private apiKey: string;
  private axiosInstance;

  constructor() {
    const difyConfig = config.api.dify;
    if (!difyConfig) {
      throw new Error('Dify 配置未找到');
    }
    this.baseUrl = difyConfig.baseUrl;
    this.apiKey = difyConfig.apiKey;
    this.config = {
      name: 'dify-agent',
      description: 'Dify 代理',
      model: 'gpt-3.5-turbo',
      temperature: 0.7,
      maxTokens: 2000,
    };

    // 创建专用的 axios 实例
    this.axiosInstance = http.create({
      baseURL: this.baseUrl,
      headers: {
        Authorization: `Bearer ${this.apiKey}`,
      },
    });
  }

  async initialize(config: AgentConfig): Promise<void> {
    this.config = { ...this.config, ...config };
  }

  async sendMessage(message: string, context?: Record<string, any>): Promise<AgentResponse> {
    try {
      const response = await this.axiosInstance.post('/api/chat-messages', {
        query: message,
        response_mode: 'blocking',
        conversation_id: context?.conversationId,
        user: context?.userId,
      });

      return {
        content: response.answer,
        metadata: {
          conversationId: response.conversation_id,
          messageId: response.id,
          model: this.config.model,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      console.error('发送消息失败:', error);
      throw error;
    }
  }

  getConfig(): AgentConfig {
    return { ...this.config };
  }

  async updateConfig(config: Partial<AgentConfig>): Promise<void> {
    this.config = { ...this.config, ...config };
  }

  async reset(): Promise<void> {
    // TODO: 实现重置逻辑，可能需要清除会话历史等
  }
}
