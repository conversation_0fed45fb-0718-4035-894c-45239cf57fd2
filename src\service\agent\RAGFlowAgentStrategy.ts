import { AgentStrategy, AgentConfig, AgentResponse } from './AgentStrategy';
import config from '../../config';
import http from '../../utils/axios';

export class RAGFlowAgentStrategy implements AgentStrategy {
  private config: AgentConfig;
  private baseUrl: string;
  private apiKey: string;
  private axiosInstance;

  constructor() {
    const ragflowConfig = config.api.ragflow;
    if (!ragflowConfig) {
      throw new Error('RAGFlow 配置未找到');
    }
    this.baseUrl = ragflowConfig.baseUrl;
    this.apiKey = ragflowConfig.apiKey;
    this.config = {
      name: 'ragflow-agent',
      description: 'RAGFlow 代理',
      model: 'gpt-3.5-turbo',
      temperature: 0.7,
      maxTokens: 2000,
    };

    // 创建专用的 axios 实例
    this.axiosInstance = http.create({
      baseURL: this.baseUrl,
      headers: {
        Authorization: `Bearer ${this.api<PERSON>ey}`,
      },
    });
  }

  async initialize(config: AgentConfig): Promise<void> {
    this.config = { ...this.config, ...config };
  }

  async sendMessage(message: string, context?: Record<string, any>): Promise<AgentResponse> {
    try {
      const response = await this.axiosInstance.post('/api/chat', {
        message,
        conversation_id: context?.conversationId,
        user_id: context?.userId,
        model: this.config.model,
        temperature: this.config.temperature,
        max_tokens: this.config.maxTokens,
      });

      return {
        content: response.data.answer,
        metadata: {
          conversationId: response.data.conversation_id,
          messageId: response.data.message_id,
          model: this.config.model,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      console.error('发送消息失败:', error);
      throw error;
    }
  }

  getConfig(): AgentConfig {
    return { ...this.config };
  }

  async updateConfig(config: Partial<AgentConfig>): Promise<void> {
    this.config = { ...this.config, ...config };
  }

  async reset(): Promise<void> {
    // TODO: 实现重置逻辑，可能需要清除会话历史等
  }
}
