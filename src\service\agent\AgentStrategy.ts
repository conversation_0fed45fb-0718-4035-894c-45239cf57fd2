export interface AgentConfig {
  name: string;
  description?: string;
  model?: string;
  temperature?: number;
  maxTokens?: number;
  systemPrompt?: string;
}

export interface AgentResponse {
  content: string;
  metadata?: Record<string, any>;
}

export interface AgentStrategy {
  // 初始化代理
  initialize(config: AgentConfig): Promise<void>;

  // 发送消息给代理
  sendMessage(message: string, context?: Record<string, any>): Promise<AgentResponse>;

  // 获取代理配置
  getConfig(): AgentConfig;

  // 更新代理配置
  updateConfig(config: Partial<AgentConfig>): Promise<void>;

  // 重置代理状态
  reset(): Promise<void>;
}
