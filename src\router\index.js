import { createRouter, createWebHistory } from 'vue-router';
import config from '@/config';

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: () => import('../views/DocumentManager.vue'),
      meta: {
        title: '文档管理'
      }
    },
    {
      path: '/search',
      name: 'search',
      component: () => import('../views/DocumentSearch.vue'),
      meta: {
        title: '文档检索'
      }
    },
  ],
});

// 设置页面标题
router.beforeEach((to, from, next) => {
  document.title = to.meta.title ? `${to.meta.title} - ${config.app.title}` : config.app.title;
  next();
});

export default router;
