@import "tailwindcss";

/* 自定义组件样式 */


/* 基础样式 */
html,
body {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  height: 100%;
  font-family: var(--font-family);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: var(--text-color);
  background-color: var(--surface-ground);
}

/* PrimeVue组件样式覆盖 */
.p-component {
  font-family: var(--font-family);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
